import requests
from django.shortcuts import render

def home(request):
    weather_data = None
    if 'city' in request.GET:
        city = request.GET['city']
        api_key = "YOUR_API_KEY"   # replace with your key
        url = f"http://api.openweathermap.org/data/2.5/forecast?q={city}&appid={api_key}&units=metric"
        response = requests.get(url).json()

        if response.get("cod") != "404":
            weather_data = {
                "city": response["city"]["name"],
                "country": response["city"]["country"],
                "current_temp": response["list"][0]["main"]["temp"],
                "condition": response["list"][0]["weather"][0]["description"],
                "forecast": [
                    {
                        "date": item["dt_txt"],
                        "temp": item["main"]["temp"],
                        "condition": item["weather"][0]["description"]
                    }
                    for item in response["list"][:5]  # show next 5 forecasts
                ]
            }
    return render(request, "weather/home.html", {"weather": weather_data})
